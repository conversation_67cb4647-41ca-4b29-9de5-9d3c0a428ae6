# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=წინა გვერდი
previous_label=წინა
next.title=შემდეგი გვერდი
next_label=შემდეგი

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=გვერდი
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}-დან
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} {{pagesCount}}-დან)

zoom_out.title=ზომის შემცირება
zoom_out_label=დაშორება
zoom_in.title=ზომის გაზრდა
zoom_in_label=მოახლოება
zoom.title=ზომა
presentation_mode.title=ჩვენების რეჟიმზე გადართვა
presentation_mode_label=ჩვენების რეჟიმი
open_file.title=ფაილის გახსნა
open_file_label=გახსნა
print.title=ამობეჭდვა
print_label=ამობეჭდვა
download.title=ჩამოტვირთვა
download_label=ჩამოტვირთვა
bookmark.title=მიმდინარე ხედი (ასლის აღება ან გახსნა ახალ ფანჯარაში)
bookmark_label=მიმდინარე ხედი

# Secondary toolbar and context menu
tools.title=ხელსაწყოები
tools_label=ხელსაწყოები
first_page.title=პირველ გვერდზე გადასვლა
first_page_label=პირველ გვერდზე გადასვლა
last_page.title=ბოლო გვერდზე გადასვლა
last_page_label=ბოლო გვერდზე გადასვლა
page_rotate_cw.title=საათის ისრის მიმართულებით შებრუნება
page_rotate_cw_label=მარჯვნივ გადაბრუნება
page_rotate_ccw.title=საათის ისრის საპირისპიროდ შებრუნება
page_rotate_ccw_label=მარცხნივ გადაბრუნება

cursor_text_select_tool.title=მოსანიშნი მაჩვენებლის გამოყენება
cursor_text_select_tool_label=მოსანიშნი მაჩვენებელი
cursor_hand_tool.title=გადასაადგილებელი მაჩვენებლის გამოყენება
cursor_hand_tool_label=გადასაადგილებელი

scroll_page.title=გვერდზე გადაადგილების გამოყენება
scroll_page_label=გვერდზე გადაადგილება
scroll_vertical.title=გვერდების შვეულად ჩვენება
scroll_vertical_label=შვეული გადაადგილება
scroll_horizontal.title=გვერდების თარაზულად ჩვენება
scroll_horizontal_label=განივი გადაადგილება
scroll_wrapped.title=გვერდების ცხრილურად ჩვენება
scroll_wrapped_label=ცხრილური გადაადგილება

spread_none.title=ორ გვერდზე გაშლის გარეშე
spread_none_label=ცალგვერდიანი ჩვენება
spread_odd.title=ორ გვერდზე გაშლა, კენტი გვერდიდან დაწყებული
spread_odd_label=ორ გვერდზე კენტიდან
spread_even.title=ორ გვერდზე გაშლა, ლუწი გვერდიდან დაწყებული
spread_even_label=ორ გვერდზე ლუწიდან

# Document properties dialog box
document_properties.title=დოკუმენტის შესახებ…
document_properties_label=დოკუმენტის შესახებ…
document_properties_file_name=ფაილის სახელი:
document_properties_file_size=ფაილის მოცულობა:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} კბ ({{size_b}} ბაიტი)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} მბ ({{size_b}} ბაიტი)
document_properties_title=სათაური:
document_properties_author=შემქმნელი:
document_properties_subject=თემა:
document_properties_keywords=საკვანძო სიტყვები:
document_properties_creation_date=შექმნის დრო:
document_properties_modification_date=ჩასწორების დრო:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=გამომშვები:
document_properties_producer=PDF-გამომშვები:
document_properties_version=PDF-ვერსია:
document_properties_page_count=გვერდები:
document_properties_page_size=გვერდის ზომა:
document_properties_page_size_unit_inches=დუიმი
document_properties_page_size_unit_millimeters=მმ
document_properties_page_size_orientation_portrait=შვეულად
document_properties_page_size_orientation_landscape=თარაზულად
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=მსუბუქი ვებჩვენება:
document_properties_linearized_yes=დიახ
document_properties_linearized_no=არა
document_properties_close=დახურვა

print_progress_message=დოკუმენტი მზადდება ამოსაბეჭდად…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=გაუქმება

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=გვერდითა ზოლის გამოჩენა/დამალვა
toggle_sidebar_notification2.title=გვერდითი ზოლის გამოჩენა (შეიცავს სარჩევს/დანართს/ფენებს)
toggle_sidebar_label=გვერდითა ზოლის გამოჩენა/დამალვა
document_outline.title=დოკუმენტის სარჩევის ჩვენება (ორმაგი წკაპით თითოეულის ჩამოშლა/აკეცვა)
document_outline_label=დოკუმენტის სარჩევი
attachments.title=დანართების ჩვენება
attachments_label=დანართები
layers.title=ფენების გამოჩენა (ორმაგი წკაპით ყველა ფენის ნაგულისხმევზე დაბრუნება)
layers_label=ფენები
thumbs.title=შეთვალიერება
thumbs_label=ესკიზები
current_outline_item.title=მიმდინარე გვერდის მონახვა სარჩევში
current_outline_item_label=მიმდინარე გვერდი სარჩევში
findbar.title=პოვნა დოკუმენტში
findbar_label=ძიება

additional_layers=დამატებითი ფენები
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=გვერდი {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=გვერდი {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=გვერდის შეთვალიერება {{page}}

# Find panel button title and messages
find_input.title=ძიება
find_input.placeholder=პოვნა დოკუმენტში…
find_previous.title=ფრაზის წინა კონტექსტის პოვნა
find_previous_label=წინა
find_next.title=ფრაზის შემდეგი კონტექსტის პოვნა
find_next_label=შემდეგი
find_highlight=ყველას მონიშვნა
find_match_case_label=მთავრულით
find_match_diacritics_label=ნიშნებით
find_entire_word_label=მთლიანი სიტყვები
find_reached_top=მიღწეულია დოკუმენტის დასაწყისი, გრძელდება ბოლოდან
find_reached_bottom=მიღწეულია დოკუმენტის ბოლო, გრძელდება დასაწყისიდან
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} / {{total}} თანხვედრიდან
find_match_count[two]={{current}} / {{total}} თანხვედრიდან
find_match_count[few]={{current}} / {{total}} თანხვედრიდან
find_match_count[many]={{current}} / {{total}} თანხვედრიდან
find_match_count[other]={{current}} / {{total}} თანხვედრიდან
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=არანაკლებ {{limit}} თანხვედრა
find_match_count_limit[one]=არანაკლებ {{limit}} თანხვედრა
find_match_count_limit[two]=არანაკლებ {{limit}} თანხვედრა
find_match_count_limit[few]=არანაკლებ {{limit}} თანხვედრა
find_match_count_limit[many]=არანაკლებ {{limit}} თანხვედრა
find_match_count_limit[other]=არანაკლებ {{limit}} თანხვედრა
find_not_found=ფრაზა ვერ მოიძებნა

# Error panel labels
error_more_info=ვრცლად
error_less_info=შემოკლებულად
error_close=დახურვა
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=შეტყობინება: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=სტეკი: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ფაილი: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ხაზი: {{line}}
rendering_error=შეცდომა, გვერდის ჩვენებისას.

# Predefined zoom values
page_scale_width=გვერდის სიგანეზე
page_scale_fit=მთლიანი გვერდი
page_scale_auto=ავტომატური
page_scale_actual=საწყისი ზომა
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=ჩატვირთვა…
loading_error=შეცდომა, PDF-ფაილის ჩატვირთვისას.
invalid_file_error=არამართებული ან დაზიანებული PDF-ფაილი.
missing_file_error=ნაკლული PDF-ფაილი.
unexpected_response_error=სერვერის მოულოდნელი პასუხი.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} შენიშვნა]
password_label=შეიყვანეთ პაროლი PDF-ფაილის გასახსნელად.
password_invalid=არასწორი პაროლი. გთხოვთ, სცადოთ ხელახლა.
password_ok=კარგი
password_cancel=გაუქმება

printing_not_supported=გაფრთხილება: ამობეჭდვა ამ ბრაუზერში არაა სრულად მხარდაჭერილი.
printing_not_ready=გაფრთხილება: PDF სრულად ჩატვირთული არაა, ამობეჭდვის დასაწყებად.
web_fonts_disabled=ვებშრიფტები გამორთულია: ჩაშენებული PDF-შრიფტების გამოყენება ვერ ხერხდება.

# Editor
editor_none.title=შენიშვნის ჩასწორების გათიშვა
editor_none_label=ჩასწორების გათიშვა
editor_free_text.title=FreeText-სახის შენიშვნის დართვა
editor_free_text_label=FreeText-სახის შენიშვნა
editor_ink.title=ხელნაწერი შენიშვნის დართვა
editor_ink_label=ხელნაწერი შენიშვნა

freetext_default_content=შეიყვანეთ რამე ტექსტი…

free_text_default_content=შეიყვანეთ ტექსტი…

# Editor Parameters
editor_free_text_font_color=შრიფტის ფერი
editor_free_text_font_size=შრიფტის ზომა
editor_ink_line_color=ხაზის ფერი
editor_ink_line_thickness=ხაზის სისქე

# Editor Parameters
editor_free_text_color=ფერი
editor_free_text_size=ზომა
editor_ink_color=ფერი
editor_ink_thickness=სისქე
editor_ink_opacity=გაუმჭვირვალობა

# Editor aria
editor_free_text_aria_label=FreeText-ჩამსწორებელი
editor_ink_aria_label=ხელნაწერის ჩამსწორებელი
editor_ink_canvas_aria_label=მომხმარებლის შექმნილი სურათი
