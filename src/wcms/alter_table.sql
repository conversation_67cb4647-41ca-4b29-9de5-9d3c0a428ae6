ALTER TABLE HEROG_FUNDS.FUND_FOCUS_LIST ADD DISPLAY_SEQ INT NULL;

ALTER TABLE HEROG_FUNDS.FSS_CTG_EXPT_RETURN
    ADD COLUMN WCMS_CREATOR varchar(100) NULL COMMENT 'Creator',
    ADD COLUMN WCMS_UPDATED_DT datetime DEFAULT (now()) NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated date',
    ADD COLUMN WCMS_CREATED_DT datetime DEFAULT (now()) NOT NULL COMMENT 'Created date';
;

ALTER TABLE HEROG_TFEX_ADM.TFEX_ADMIN_FINANSIA_RECOMMEND

    ADD COLUMN CARD_OVERVIEW_URL varchar(100) NULL COMMENT 'card overview url',
    ADD COLUMN CARD_FULL_URL varchar(100) NULL COMMENT 'card full url',
    ADD COLUMN WCMS_CREATOR varchar(100) NULL COMMENT 'Creator',
    ADD COLUMN WCMS_UPDATED_DT datetime DEFAULT (now()) NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated date',
                                                                                                                      ADD COLUMN WCMS_CREATED_DT datetime DEFAULT (now()) NOT NULL COMMENT 'Created date';
alter table FUND_FOCUS_LIST
    modify INPUT_DATE date null comment 'Automatically input date in YYYY-MM-DD format';

